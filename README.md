# 🏋️‍♂️ FitAI - Your Personal AI Fitness Coach

<div align="center">
  <img src="ai.png" alt="FitAI Logo" width="200" height="200">
  
  [![License: ISC](https://img.shields.io/badge/License-ISC-blue.svg)](https://opensource.org/licenses/ISC)
  [![Version](https://img.shields.io/badge/version-1.0.0-green.svg)](https://github.com/yourusername/ai-powered-fitness-coach)
  [![JavaScript](https://img.shields.io/badge/JavaScript-ES6+-yellow.svg)](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
  [![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
  
  **Transform your fitness journey with AI-powered personalized workout plans and nutrition tracking**
</div>

---

## 🌟 Features

### 🤖 **AI-Powered Chatbot**
- Intelligent fitness assistant powered by Google Gemini AI
- Personalized workout recommendations
- Nutrition advice and meal planning
- Real-time fitness guidance and motivation

### 💪 **Comprehensive Workout Library**
- **Chest Exercises**: 5 targeted chest workouts with animated demonstrations
- **Biceps Training**: Complete bicep workout routines
- **Triceps Focus**: Dedicated tricep strengthening exercises
- **Shoulder Development**: Full shoulder workout programs
- **Leg Day**: Comprehensive lower body training sessions

### 📊 **Smart Tracking & Analytics**
- Workout progress monitoring
- Nutrition tracking with calorie counter
- Personal fitness dashboard
- Goal setting and achievement tracking

### 🔔 **Smart Reminders**
- Customizable workout reminders
- Nutrition tracking alerts
- Progress milestone notifications
- Motivational push notifications

### 📱 **Modern User Experience**
- Responsive design for all devices
- Smooth animations with GSAP
- Intuitive navigation
- Dark/Light theme support
- Voice interaction capabilities

---

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- Python 3.8+
- Google Gemini API key

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Vishnurockyedwards/AI-fitness-coach.git
   cd ai-powered-fitness-coach
   ```

2. **Install JavaScript dependencies**
   ```bash
   npm install
   ```

3. **Install Python dependencies**
   ```bash
   pip install google-generativeai python-dotenv
   ```

4. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

5. **Launch the application**
   ```bash
   # Open index.html in your browser or use a local server
   python -m http.server 8000
   # Navigate to http://localhost:8000
   ```

---

## 🏗️ Project Structure

```
ai-powered-fitness-coach/
├── 📁 gemini/                 # AI chatbot backend
│   └── gemini_chatbot.py      # Gemini AI integration
├── 📁 static/                 # Static assets
├── 📁 templates/              # HTML templates
├── 📁 exercise/               # Exercise data and archives
├── 🎬 *.gif                   # Exercise demonstration animations
├── 🖼️ *.png, *.svg, *.jpg     # UI assets and icons
├── 📄 index.html              # Main application page
├── 🎨 style.css               # Application styling
├── ⚡ script.js               # Core JavaScript functionality
├── 📊 calories.csv            # Nutrition database
└── 📦 package.json            # Node.js dependencies
```

---

## 🎯 Core Functionality

### 🏠 **Home Dashboard**
- Welcome interface with personalized greetings
- Quick access to all major features
- Progress overview and statistics
- Recent activity feed

### 💪 **My Workouts**
- Browse exercise library by muscle group
- Create custom workout routines
- Track workout completion
- View exercise demonstrations

### 🥗 **Nutrition Tracker**
- Log daily meals and snacks
- Calorie counting and macronutrient tracking
- Nutritional goal setting
- Meal planning suggestions

### ⏰ **Smart Reminders**
- Set workout reminders
- Nutrition tracking alerts
- Custom notification scheduling
- Progress milestone celebrations

### ⚙️ **Settings**
- Profile customization
- Notification preferences
- Theme selection
- Data export options

---

## 🤖 AI Integration

The application leverages **Google Gemini AI** to provide:

- **Personalized Recommendations**: Tailored workout plans based on user goals
- **Intelligent Responses**: Context-aware fitness advice and motivation
- **Adaptive Learning**: Improves suggestions based on user interactions
- **Natural Conversations**: Human-like chat experience for fitness guidance

---

## 🛠️ Technologies Used

### Frontend
- **HTML5** - Semantic markup and structure
- **CSS3** - Modern styling with animations
- **JavaScript (ES6+)** - Interactive functionality
- **GSAP** - Smooth animations and transitions
- **Bootstrap 5** - Responsive grid system

### Backend
- **Python** - AI chatbot server
- **Google Gemini AI** - Natural language processing
- **Node.js** - Package management

### Assets
- **Animated GIFs** - Exercise demonstrations
- **SVG Icons** - Scalable vector graphics
- **CSV Data** - Nutrition database

---

## 📱 Responsive Design

FitAI is fully responsive and optimized for:
- 🖥️ **Desktop** - Full-featured experience
- 📱 **Mobile** - Touch-optimized interface
- 📟 **Tablet** - Adaptive layout

---

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

---
<!-- 
## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details. -->

---

## 🙏 Acknowledgments

- Google Gemini AI for powering our intelligent chatbot
- GSAP for smooth animations
- Bootstrap for responsive design components
- The fitness community for inspiration and feedback

---

<div align="center">
  <h3>🌟 Start Your Fitness Journey Today! 🌟</h3>
  <p>Transform your body, mind, and lifestyle with FitAI</p>
  
  [⭐ Star this repo](https://github.com/yourusername/ai-powered-fitness-coach) | [🐛 Report Bug](https://github.com/yourusername/ai-powered-fitness-coach/issues) | [💡 Request Feature](https://github.com/yourusername/ai-powered-fitness-coach/issues)
</div>
