* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Gill Sans MT', '<PERSON>', <PERSON><PERSON><PERSON>, 'Trebuchet MS', sans-serif;
}

:root {
    --primary: #ff5722;
    --secondary: #2c3e50;
    --light: #ecf0f1;
    --dark: #1a1a1a;
    --success: #2ecc71;
    --warning: #f39c12;
    --danger: #e74c3c;
    --gray: #95a5a6;
    --card-bg: rgba(255, 255, 255, 0.9);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    width: 100%;
    min-height: 100vh;
    background-image: url("bg.jpg");
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: var(--dark);
    line-height: 1.6;
}

/* Particles.js Background */
/*particles-js {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}*/

.main-container {
    position: relative;
    width: 100%;
    min-height: 100vh;
}

/* Navigation */
nav {
    width: 100%;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5%;
    background-color: rgba(0, 0, 0, 0.85);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Removed PillNav integration overrides */

.logo {
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.right-nav {
    display: flex;
    align-items: center;
    gap: 20px;
}

.searchbox {
    padding: 10px 20px;
    font-size: 1rem;
    color: white;
    border-radius: 50px;
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.4);
    width: 250px;
    transition: var(--transition);
}

.searchbox:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.25);
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2);
    width: 300px;
}

.searchbox::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.chat-btn {
    background: var(--primary);
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.chat-btn img {
    width: 24px;
    height: 24px;
}

.chat-btn:hover {
    transform: scale(1.1);
    background-color: #e64a19;
}

/* Hero Section */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 5%;
    background-color: rgba(0, 0, 0, 0.5);
}

.hero-content {
    max-width: 800px;
}

.hero h1 {
    font-size: 3.5rem;
    color: white;
    margin-bottom: 20px;
    line-height: 1.2;
}

/* Typewriter */
#typewriter {
    white-space: nowrap;
}

.cursor {
    display: inline-block;
    margin-left: 6px;
    width: 1ch;
    color: var(--primary);
    animation: blink 1s steps(1) infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    50.01%, 100% { opacity: 0; }
}

.hero p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;
}

.cta-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: bold;
}

.cta-btn:hover {
    background-color: #e64a19;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Content Wrapper */
.content-wrapper {
    padding: 120px 5% 60px;
}

/* User Form Section */
.user-form-section {
    max-width: 800px;
    margin: 0 auto 60px;
    background-color: var(--card-bg);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.user-form-section h2 {
    color: var(--secondary);
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--secondary);
}

.help-text {
    display: block;
    font-size: 0.8rem;
    color: var(--gray);
    margin-top: 5px;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.2);
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
}

.submit-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 14px 30px;
    font-size: 1.1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: block;
    width: 100%;
    margin-top: 30px;
    font-weight: bold;
}

.submit-btn:hover {
    background-color: #e64a19;
}

/* Workout Plan Section */
.workout-plan-section {
    max-width: 1000px;
    margin: 0 auto 60px;
    background-color: var(--card-bg);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.workout-plan-section h2 {
    color: var(--secondary);
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
}

.plan-info {
    background-color: rgba(44, 62, 80, 0.1);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.workout-schedule {
    margin-bottom: 40px;
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.schedule-day {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.day-header {
    background-color: var(--secondary);
    color: white;
    padding: 10px;
    font-weight: bold;
    text-align: center;
}

.day-content {
    padding: 15px;
    text-align: center;
}

.exercise-gallery h3 {
    color: var(--secondary);
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.exercise-item {
    display: flex;
    align-items: flex-start;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-bottom: 15px;
    transition: var(--transition);
}

.exercise-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.exercise-item img {
    width: 150px;
    height: 150px;
    object-fit: cover;
}

.exercise-details {
    padding: 15px;
    flex: 1;
}

.exercise-details h4 {
    color: var(--secondary);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.exercise-details p {
    margin-bottom: 10px;
    font-size: 0.95rem;
    color: #555;
}

.user-stats {
    margin-bottom: 25px;
}

.user-stats h3, .plan-overview h3 {
    color: var(--primary);
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.plan-overview {
    margin-bottom: 10px;
}

.action-btn {
    background-color: var(--secondary);
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    margin-right: 15px;
}

.action-btn:hover {
    background-color: #1a2530;
}

/* Exercise Showcase */
.exercise-showcase {
    max-width: 1200px;
    margin: 0 auto;
    background-color: var(--card-bg);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.exercise-showcase h2 {
    color: var(--secondary);
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
}

.category-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 30px;
}

.filter-btn {
    background-color: transparent;
    border: 2px solid var(--secondary);
    color: var(--secondary);
    padding: 8px 20px;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: bold;
}

.filter-btn:hover, .filter-btn.active {
    background-color: var(--secondary);
    color: white;
}

.exercises-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 25px;
}

.exercise-card {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.exercise-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

.exercise-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-bottom: 1px solid #eee;
}

.exercise-card h4 {
    padding: 15px;
    font-size: 1.1rem;
    color: var(--secondary);
    text-align: center;
}

/* Chatbot */
.chatbot-container {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 380px;
    height: 550px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    z-index: 1000;
    overflow: hidden;
    transform: translateY(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.chatbot-container:not(.hidden) {
    transform: translateY(0);
    opacity: 1;
}

.chat-header {
    background: linear-gradient(135deg, var(--primary) 0%, #ff8a65 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px 20px 0 0;
    position: relative;
}

.chat-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px 20px 0 0;
    z-index: -1;
}

.chat-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-header h3::before {
    content: '🤖';
    font-size: 1.2rem;
}

.close-btn {
    background: transparent;
    border: none;
    cursor: pointer;
}

.close-btn img {
    width: 16px;
    height: 16px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 25px 20px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 87, 34, 0.3) transparent;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(255, 87, 34, 0.3);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 87, 34, 0.5);
}

.message {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    justify-content: flex-end;
    flex-direction: row-reverse;
}

.message.user .message-content {
    background: linear-gradient(135deg, var(--primary) 0%, #ff8a65 100%);
    color: white;
    margin-left: 50px;
    border-radius: 20px 20px 5px 20px;
    box-shadow: 0 4px 12px rgba(255, 87, 34, 0.3);
}

.message.bot .message-content {
    background: rgba(255, 255, 255, 0.9);
    color: var(--dark);
    margin-right: 50px;
    border-radius: 20px 20px 20px 5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-content {
    padding: 15px 20px;
    max-width: 75%;
    word-wrap: break-word;
    line-height: 1.5;
    font-size: 0.95rem;
    position: relative;
}

.message-time {
    font-size: 0.75rem;
    color: rgba(0, 0, 0, 0.5);
    margin-top: 5px;
    text-align: right;
}

.message.user .message-time {
    color: rgba(255, 255, 255, 0.7);
    text-align: left;
}

.bot-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    flex-shrink: 0;
    border: 2px solid rgba(255, 87, 34, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Typing Indicator */
.typing-indicator .message-content {
    background: rgba(255, 255, 255, 0.9);
    padding: 15px 20px;
    border-radius: 20px 20px 20px 5px;
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary);
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }
.typing-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.chat-input {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0 0 20px 20px;
    gap: 10px;
}

.chat-input input {
    flex: 1;
    padding: 12px 18px;
    border: 1px solid rgba(255, 87, 34, 0.2);
    border-radius: 25px;
    outline: none;
    font-size: 0.95rem;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.chat-input input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
    background: white;
}

.chat-input input::placeholder {
    color: rgba(0, 0, 0, 0.5);
}

.chat-input button {
    background: var(--primary);
    border: none;
    padding: 10px 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.chat-input button:hover {
    background: #e64a19;
    transform: scale(1.05);
}

.chat-input button img {
    width: 20px;
    height: 20px;
    filter: brightness(0) invert(1);
}

#voice-btn {
    background: #4CAF50;
    color: white;
    font-size: 0.8rem;
    padding: 8px 12px;
    border-radius: 20px;
    min-width: auto;
    height: auto;
    white-space: nowrap;
}

#voice-btn:hover {
    background: #45a049;
}

/* Quick Actions */
.quick-actions {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 87, 34, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 87, 34, 0.1);
}

.quick-action-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 10px;
}

.quick-action-btn {
    display: inline-block;
    background: rgba(255, 87, 34, 0.1);
    color: var(--primary);
    border: 1px solid rgba(255, 87, 34, 0.2);
    padding: 8px 12px;
    margin: 4px;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 87, 34, 0.3);
}

/* Action Buttons */
.action-buttons-container {
    margin: 20px 0;
    padding: 15px;
    background: rgba(76, 175, 80, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(76, 175, 80, 0.1);
}

.action-buttons-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #4CAF50;
    margin-bottom: 10px;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.action-btn {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
    border: 1px solid rgba(76, 175, 80, 0.2);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.action-btn:hover {
    background: #4CAF50;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

/* Settings Section */
.settings-section {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.settings-section h2 {
    color: var(--secondary);
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
}

.settings-group {
    margin-bottom: 40px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.settings-group h3 {
    color: var(--primary);
    margin-bottom: 20px;
    font-size: 1.3rem;
    border-bottom: 2px solid rgba(255, 87, 34, 0.2);
    padding-bottom: 10px;
}

.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.settings-item:last-child {
    border-bottom: none;
}

.setting-info h4 {
    margin: 0 0 5px 0;
    color: var(--dark);
    font-size: 1.1rem;
}

.setting-info p {
    margin: 0;
    color: var(--gray);
    font-size: 0.9rem;
}

.settings-btn {
    background: var(--primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    white-space: nowrap;
}

.settings-btn:hover {
    background: #e64a19;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 87, 34, 0.3);
}

.settings-btn.secondary {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
}

.settings-btn.secondary:hover {
    background: var(--primary);
    color: white;
}

.settings-btn.danger {
    background: #f44336;
}

.settings-btn.danger:hover {
    background: #d32f2f;
    box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
}

.export-buttons {
    display: flex;
    gap: 10px;
}

.storage-info {
    background: rgba(255, 255, 255, 0.7);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.storage-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.storage-item:last-child {
    border-bottom: none;
}

.storage-label {
    font-weight: 600;
    color: var(--dark);
}

.storage-value {
    color: var(--primary);
    font-weight: bold;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.settings-select {
    padding: 8px 12px;
    border: 2px solid rgba(255, 87, 34, 0.3);
    border-radius: 8px;
    background: white;
    color: var(--dark);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.settings-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

/* Reminders Section */
.reminders-section {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px;
}

.reminders-section h2 {
    color: var(--secondary);
    margin-bottom: 10px;
    text-align: center;
    font-size: 2rem;
}

.section-description {
    text-align: center;
    color: var(--gray);
    margin-bottom: 40px;
    font-size: 1.1rem;
}

.reminder-form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.reminder-form-container h3 {
    color: var(--primary);
    margin-bottom: 25px;
    font-size: 1.3rem;
}

.reminder-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: var(--dark);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 16px;
    border: 2px solid rgba(255, 87, 34, 0.2);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
    background: white;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.days-selector {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.day-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(255, 87, 34, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    transition: all 0.3s ease;
    user-select: none;
}

.day-checkbox:hover {
    background: rgba(255, 87, 34, 0.1);
    border-color: var(--primary);
}

.day-checkbox input[type="checkbox"] {
    display: none;
}

.day-checkbox input[type="checkbox"]:checked + span {
    color: white;
}

.day-checkbox:has(input[type="checkbox"]:checked) {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
}

.day-checkbox span {
    font-weight: 600;
    font-size: 0.9rem;
}

.reminders-list-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.reminders-list-container h3 {
    color: var(--primary);
    margin-bottom: 25px;
    font-size: 1.3rem;
}

.reminders-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.reminder-item {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.reminder-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reminder-info {
    flex: 1;
}

.reminder-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 5px;
}

.reminder-details {
    display: flex;
    gap: 15px;
    align-items: center;
    font-size: 0.9rem;
    color: var(--gray);
}

.reminder-type {
    background: rgba(255, 87, 34, 0.1);
    color: var(--primary);
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
}

.reminder-time {
    font-weight: 600;
}

.reminder-days {
    font-size: 0.8rem;
}

.reminder-actions {
    display: flex;
    gap: 10px;
}

.reminder-btn {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 600;
}

.reminder-btn:hover {
    background: var(--primary);
    color: white;
}

.reminder-btn.delete {
    border-color: #f44336;
    color: #f44336;
}

.reminder-btn.delete:hover {
    background: #f44336;
    color: white;
}

.no-reminders {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray);
}

.no-reminders-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-reminders h4 {
    margin-bottom: 10px;
    color: var(--dark);
}

.notification-permissions {
    background: rgba(255, 193, 7, 0.1);
    border: 2px solid rgba(255, 193, 7, 0.3);
    border-radius: 15px;
    padding: 20px;
}

.permission-card {
    display: flex;
    align-items: center;
    gap: 20px;
}

.permission-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.permission-content h4 {
    margin-bottom: 8px;
    color: var(--dark);
}

.permission-content p {
    margin-bottom: 15px;
    color: var(--gray);
    font-size: 0.9rem;
}

/* Reminders Section */
.reminders-section {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px;
}

.reminders-section h2 {
    color: var(--secondary);
    margin-bottom: 10px;
    text-align: center;
    font-size: 2rem;
}

.section-description {
    text-align: center;
    color: var(--gray);
    margin-bottom: 40px;
    font-size: 1.1rem;
}

.reminder-form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.reminder-form-container h3 {
    color: var(--primary);
    margin-bottom: 25px;
    font-size: 1.3rem;
}

.reminder-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: var(--dark);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 16px;
    border: 2px solid rgba(255, 87, 34, 0.2);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
    background: white;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.days-selector {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.day-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(255, 87, 34, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    transition: all 0.3s ease;
    user-select: none;
}

.day-checkbox:hover {
    background: rgba(255, 87, 34, 0.1);
    border-color: var(--primary);
}

.day-checkbox input[type="checkbox"] {
    display: none;
}

.day-checkbox input[type="checkbox"]:checked + span {
    color: white;
}

.day-checkbox:has(input[type="checkbox"]:checked) {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
}

.day-checkbox span {
    font-weight: 600;
    font-size: 0.9rem;
}

.reminders-list-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.reminders-list-container h3 {
    color: var(--primary);
    margin-bottom: 25px;
    font-size: 1.3rem;
}

.reminders-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.reminder-item {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.reminder-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reminder-info {
    flex: 1;
}

.reminder-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 5px;
}

.reminder-details {
    display: flex;
    gap: 15px;
    align-items: center;
    font-size: 0.9rem;
    color: var(--gray);
}

.reminder-type {
    background: rgba(255, 87, 34, 0.1);
    color: var(--primary);
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
}

.reminder-time {
    font-weight: 600;
}

.reminder-days {
    font-size: 0.8rem;
}

.reminder-actions {
    display: flex;
    gap: 10px;
}

.reminder-btn {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 600;
}

.reminder-btn:hover {
    background: var(--primary);
    color: white;
}

.reminder-btn.delete {
    border-color: #f44336;
    color: #f44336;
}

.reminder-btn.delete:hover {
    background: #f44336;
    color: white;
}

.no-reminders {
    text-align: center;
    padding: 40px 20px;
    color: var(--gray);
}

.no-reminders-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-reminders h4 {
    margin-bottom: 10px;
    color: var(--dark);
}

.notification-permissions {
    background: rgba(255, 193, 7, 0.1);
    border: 2px solid rgba(255, 193, 7, 0.3);
    border-radius: 15px;
    padding: 20px;
}

.permission-card {
    display: flex;
    align-items: center;
    gap: 20px;
}

.permission-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.permission-content h4 {
    margin-bottom: 8px;
    color: var(--dark);
}

.permission-content p {
    margin-bottom: 15px;
    color: var(--gray);
    font-size: 0.9rem;
}



/* Navigation Links */
.nav-links {
    display: flex;
    margin-right: 20px;
    color: white;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 10px 18px;
    margin: 0 5px;
    border-radius: 5px;
    transition: var(--transition);
    font-weight: 500;
    letter-spacing: 0.5px;
    position: relative;
}

.nav-link:hover, .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

.nav-link.active {
    color: var(--primary);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 1px;
    left: 6px;
    right: 5px;
    height: 2px;
    background-color: var(--primary);
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
    margin-left: 15px;
}

.mobile-menu-toggle span {
    display: block;
    height: 3px;
    width: 100%;
    background-color: white;
    border-radius: 3px;
    transition: var(--transition);
}

/* Mobile menu toggle animation */
.mobile-menu-toggle.active span:nth-child(1) {
    transform: translateY(9px) rotate(45deg);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: translateY(-9px) rotate(-45deg);
}

/* Notification Bell */
.notification-bell {
    position: relative;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 10px;
}

.notification-bell img {
    width: 24px;
    height: 24px;
    filter: invert(1);
}

.notification-count {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--primary);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Notification Panel */
.notification-panel {
    position: fixed;
    top: 80px;
    right: 20px;
    width: 350px;
    max-height: 400px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    z-index: 999;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: var(--secondary);
    color: white;
}

.notification-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.notification-header button {
    background: transparent;
    border: none;
    color: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: var(--transition);
}

.notification-header button:hover {
    text-decoration: underline;
}

.notification-list {
    overflow-y: auto;
    padding: 10px;
    flex: 1;
}

.notification-item {
    padding: 12px;
    border-bottom: 1px solid #eee;
    transition: var(--transition);
}

.notification-item:hover {
    background-color: #f9f9f9;
}

.notification-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--secondary);
}

.notification-time {
    font-size: 0.8rem;
    color: #999;
}

/* Main Sections */
.main-section {
    width: 100%;
}

/* Action Buttons Container */
.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

/* Workout Heatmap Section */
.workout-heatmap-section {
    max-width: 1000px;
    margin: 0 auto 40px;
    background-color: var(--card-bg);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.workout-heatmap-section h2 {
    color: var(--secondary);
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
}

.heatmap-container {
    width: 100%;
}

.heatmap-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.heatmap-stats {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.heatmap-stat {
    text-align: center;
    min-width: 80px;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--gray);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.heatmap-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.heatmap-btn {
    background: var(--primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: bold;
}

.heatmap-btn:hover {
    background: #e64a19;
    transform: translateY(-2px);
}

.heatmap-btn.secondary {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
}

.heatmap-btn.secondary:hover {
    background: var(--primary);
    color: white;
}

.heatmap-calendar {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    position: relative;
}

.heatmap-months {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: var(--gray);
    font-weight: bold;
}

.heatmap-grid {
    display: grid;
    grid-template-columns: repeat(53, 1fr);
    gap: 2px;
    margin-bottom: 15px;
}

.heatmap-day {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.heatmap-day[data-level="0"] {
    background-color: #ebedf0;
}

.heatmap-day[data-level="1"] {
    background-color: #c6e48b;
}

.heatmap-day[data-level="2"] {
    background-color: #7bc96f;
}

.heatmap-day[data-level="3"] {
    background-color: #239a3b;
}

.heatmap-day[data-level="4"] {
    background-color: #196127;
}

.heatmap-day:hover {
    transform: scale(1.2);
    z-index: 10;
}

.heatmap-legend {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 5px;
    font-size: 0.8rem;
    color: var(--gray);
}

.legend-colors {
    display: flex;
    gap: 2px;
}

.legend-color {
    width: 10px;
    height: 10px;
    border-radius: 2px;
}

.legend-color[data-level="0"] {
    background-color: #ebedf0;
}

.legend-color[data-level="1"] {
    background-color: #c6e48b;
}

.legend-color[data-level="2"] {
    background-color: #7bc96f;
}

.legend-color[data-level="3"] {
    background-color: #239a3b;
}

.legend-color[data-level="4"] {
    background-color: #196127;
}

.heatmap-tooltip {
    position: absolute;
    background: var(--dark);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.2s ease;
    white-space: nowrap;
}

.heatmap-tooltip.show {
    opacity: 1;
}



/* Saved Workouts Section */
.saved-workouts-section {
    max-width: 1000px;
    margin: 0 auto 60px;
    background-color: var(--card-bg);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.saved-workouts-list {
    margin-top: 30px;
}

.saved-workout-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.saved-workout-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.saved-workout-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.saved-workout-title {
    font-size: 1.3rem;
    color: var(--secondary);
    font-weight: bold;
}

.saved-workout-actions {
    display: flex;
    gap: 10px;
}

.saved-workout-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    color: var(--gray);
    transition: var(--transition);
}

.saved-workout-btn:hover {
    color: var(--primary);
}

.saved-workout-info {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.saved-workout-stat {
    background-color: #f5f5f5;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
}

/* Nutrition Tracker Section */
.nutrition-tracker-section {
    max-width: 1000px;
    margin: 0 auto 60px;
    background-color: var(--card-bg);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.nutrition-summary {
    display: flex;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.calorie-card {
    flex: 1;
    min-width: 300px;
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: var(--shadow);
}

.calorie-card h3 {
    color: var(--secondary);
    margin-bottom: 20px;
    text-align: center;
}

.calorie-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 10px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    position: relative;
}

.calorie-circle::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 10px solid var(--primary);
    border-right-color: transparent;
    border-bottom-color: transparent;
    transform: rotate(-45deg);
}

.calorie-circle #currentCalories {
    font-size: 2.2rem;
    font-weight: bold;
    color: var(--secondary);
}

.calorie-label {
    font-size: 0.9rem;
    color: var(--gray);
}

.macros {
    margin-top: 20px;
}

.macro {
    margin-bottom: 15px;
}

.macro-label {
    display: block;
    margin-bottom: 5px;
    font-size: 0.9rem;
    color: var(--secondary);
}

.macro-bar {
    height: 10px;
    background-color: #f0f0f0;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 5px;
}

.macro-progress {
    height: 100%;
    border-radius: 5px;
    width: 0%;
    transition: width 0.5s ease;
}

.macro-progress.protein {
    background-color: #4CAF50;
}

.macro-progress.carbs {
    background-color: #2196F3;
}

.macro-progress.fat {
    background-color: #FFC107;
}

.macro-value {
    font-size: 0.9rem;
    color: var(--gray);
    text-align: right;
    display: block;
}

.meal-history {
    flex: 1;
    min-width: 300px;
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: var(--shadow);
}

/* Wearable Vitals */
.vitals-card {
    flex: 1;
    min-width: 300px;
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: var(--shadow);
}

.vitals-row {
    display: flex;
    gap: 20px;
    margin: 10px 0 20px;
}

.vital {
    flex: 1;
    background: #f7f7f7;
    border-radius: 8px;
    padding: 12px 16px;
}

.vital-label {
    color: var(--secondary);
    font-size: 0.9rem;
    margin-bottom: 6px;
}

.vital-value {
    font-size: 1.4rem;
    font-weight: bold;
}

.vitals-actions {
    display: flex;
    gap: 10px;
}

.vitals-status {
    margin-top: 10px;
    color: var(--gray);
    font-size: 0.9rem;
}

.meal-history h3 {
    color: var(--secondary);
    margin-bottom: 20px;
}

.meal-list {
    max-height: 250px;
    overflow-y: auto;
}

.meal-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.meal-info {
    flex: 1;
}

.meal-name {
    font-weight: bold;
    color: var(--secondary);
    margin-bottom: 3px;
}

.meal-type {
    font-size: 0.8rem;
    color: var(--gray);
    text-transform: capitalize;
}

.meal-macros {
    display: flex;
    gap: 10px;
    font-size: 0.8rem;
}

.meal-macros span {
    color: var(--gray);
}

.meal-calories {
    font-weight: bold;
    color: var(--primary);
}

.delete-meal {
    background: transparent;
    border: none;
    color: var(--danger);
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
}

.delete-meal:hover {
    opacity: 1;
}

.add-food-section {
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.add-food-section h3 {
    color: var(--secondary);
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
    min-width: 150px;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.secondary-btn {
    background-color: transparent;
    border: 1px solid var(--secondary);
    color: var(--secondary);
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: bold;
}

.secondary-btn:hover {
    background-color: var(--secondary);
    color: white;
}

.food-search-results {
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.food-search-results h3 {
    color: var(--secondary);
    margin-bottom: 20px;
}

.food-categories-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
    max-height: 120px;
    overflow-y: auto;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.category-filter-btn {
    background-color: #eee;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: var(--transition);
    white-space: nowrap;
}

.category-filter-btn:hover, .category-filter-btn.active {
    background-color: var(--primary);
    color: white;
}

.search-box {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.search-box input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
}

.search-box button {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.search-box button:hover {
    background-color: #e64a19;
}

.food-results-list {
    max-height: 350px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 8px;
}

.food-result-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.food-result-item:hover {
    background-color: #f5f5f5;
}

.food-result-name {
    font-weight: bold;
    color: var(--secondary);
    margin-bottom: 5px;
}

.food-result-category {
    font-size: 0.8rem;
    color: var(--gray);
    margin-bottom: 5px;
    background-color: #f0f0f0;
    padding: 2px 8px;
    border-radius: 10px;
    display: inline-block;
}

.food-result-calories {
    color: var(--primary);
    font-size: 0.9rem;
}

.food-result-macros {
    display: flex;
    gap: 15px;
    margin-top: 5px;
    font-size: 0.8rem;
    color: var(--gray);
}

.nutrition-history {
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: var(--shadow);
}

.nutrition-history h3 {
    color: var(--secondary);
    margin-bottom: 20px;
}

.chart-container {
    height: 300px;
}

/* Hydration Tracker Section */
.hydration-tracker-section {
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.hydration-tracker-section h3 {
    color: var(--secondary);
    margin-bottom: 20px;
    text-align: center;
}

.hydration-content {
    text-align: center;
}

.hydration-reminder {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #2196f3;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    display: none;
    color: #1976d2;
}

.hydration-reminder.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.hydration-main {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.water-bottle-container {
    position: relative;
    width: 120px;
    height: 200px;
}

.water-bottle {
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, #e3f2fd 0%, #bbdefb 100%);
    border: 3px solid #2196f3;
    border-radius: 15px 15px 25px 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
}

.water-bottle::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 15px;
    background: #2196f3;
    border-radius: 5px 5px 0 0;
}

.water-level {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, #1976d2 0%, #42a5f5 100%);
    transition: height 0.8s ease;
    border-radius: 0 0 22px 22px;
    height: 0%;
}

.water-level::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.hydration-stats {
    display: flex;
    gap: 30px;
    justify-content: center;
    flex-wrap: wrap;
}

.hydration-stat {
    text-align: center;
    min-width: 80px;
}

.hydration-stat h4 {
    color: #2196f3;
    margin-bottom: 5px;
    font-size: 1.5rem;
    font-weight: bold;
}

.hydration-stat p {
    color: var(--gray);
    font-size: 0.9rem;
    margin: 0;
}

.hydration-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 25px 0;
    flex-wrap: wrap;
}

.water-btn {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    border: none;
    padding: 12px 18px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.water-btn:hover {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

.water-btn.reset {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.water-btn.reset:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
    box-shadow: 0 4px 8px rgba(244, 67, 54, 0.4);
}

.hydration-tip {
    color: var(--gray);
    font-size: 0.9rem;
    margin: 20px 0 0;
    font-style: italic;
}

/* Reminders Section */
.reminders-section {
    max-width: 1000px;
    margin: 0 auto 60px;
    background-color: var(--card-bg);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.reminders-container {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.active-reminders, .add-reminder {
    flex: 1;
    min-width: 300px;
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: var(--shadow);
}

.active-reminders h3, .add-reminder h3 {
    color: var(--secondary);
    margin-bottom: 20px;
}

.reminders-list {
    max-height: 400px;
    overflow-y: auto;
}

.reminder-item {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
}

.reminder-type {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7rem;
    text-transform: uppercase;
    font-weight: bold;
}

.reminder-type.workout {
    background-color: #e3f2fd;
    color: #1976d2;
}

.reminder-type.nutrition {
    background-color: #e8f5e9;
    color: #388e3c;
}

.reminder-type.water {
    background-color: #e1f5fe;
    color: #0288d1;
}

.reminder-type.other {
    background-color: #f5f5f5;
    color: #616161;
}

.reminder-title {
    font-weight: bold;
    color: var(--secondary);
    margin-bottom: 10px;
    padding-right: 70px;
}

.reminder-time {
    display: block;
    font-size: 0.9rem;
    color: var(--gray);
    margin-bottom: 5px;
}

.reminder-days {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.reminder-day {
    padding: 3px 6px;
    background-color: #f0f0f0;
    border-radius: 3px;
    font-size: 0.7rem;
    text-transform: uppercase;
}

.reminder-note {
    font-size: 0.9rem;
    color: #555;
    margin-top: 10px;
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.reminder-controls {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
}

.reminder-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    transition: var(--transition);
}

.edit-reminder {
    color: var(--secondary);
}

.delete-reminder {
    color: var(--danger);
}

.reminder-btn:hover {
    opacity: 0.8;
}

.day-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.day-selector label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
}

.day-selector input {
    margin-right: 5px;
}

.reminder-permissions {
    margin-top: 30px;
}

.permission-card {
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: var(--shadow);
    text-align: center;
}

.permission-card h3 {
    color: var(--secondary);
    margin-bottom: 15px;
}

.permission-card p {
    margin-bottom: 20px;
    color: #555;
}

/* Empty States */
.empty-state {
    text-align: center;
    color: var(--gray);
    padding: 30px 0;
    font-style: italic;
}

/* Enhanced Responsive Styles - Mobile First Approach */

/* Extra Large Devices (1400px and up) */
@media (min-width: 1400px) {
    .main-container {
        max-width: 1600px;
        margin: 0 auto;
    }

    .hero-title {
        font-size: 4.5rem;
    }

    .workout-heatmap-section,
    .saved-workouts-section,
    .nutrition-tracker-section {
        max-width: 1200px;
    }
}

/* Large Devices (1200px to 1399px) */
@media (min-width: 1200px) and (max-width: 1399px) {
    .hero-title {
        font-size: 4rem;
    }

    .nutrition-summary {
        gap: 40px;
    }
}

/* Medium Devices (992px to 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .main-container {
        padding: 0 20px;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .heatmap-stats {
        gap: 25px;
    }

    .nutrition-summary {
        gap: 25px;
    }
}

/* Tablet and Small Desktop (768px to 991px) */
@media (max-width: 991px) {
    .hero {
        padding: 0 3%;
    }

    .hero-title {
        font-size: 3rem;
        line-height: 1.1;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        margin-bottom: 25px;
    }

    .enhanced-btn {
        padding: 12px 35px;
        font-size: 1.1rem;
    }

    .floating-element {
        font-size: 1.8rem;
        opacity: 0.6;
    }

    nav {
        padding: 0 3%;
        height: 70px;
    }

    .logo {
        font-size: 2rem;
    }

    .content-wrapper {
        padding: 100px 3% 60px;
    }

    .workout-heatmap-section,
    .saved-workouts-section,
    .nutrition-tracker-section,
    .reminders-section {
        margin: 0 15px 40px;
        padding: 35px 25px;
    }

    .calorie-card,
    .meal-history,
    .vitals-card {
        min-width: 100%;
        margin-bottom: 20px;
    }

    .schedule-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
    }

    .exercise-list {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .action-buttons {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .action-btn {
        flex: 1;
        min-width: 180px;
        margin: 5px;
    }
}

/* Original Tablet Styles (max-width: 992px) */
@media (max-width: 992px) {
    .nutrition-summary,
    .reminders-container {
        flex-direction: column;
    }

    .searchbox {
        width: 180px;
    }

    .searchbox:focus {
        width: 220px;
    }

    .nav-links {
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.9);
        flex-direction: column;
        padding: 20px 0;
        text-align: center;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .nav-links.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-link {
        margin: 5px 0;
        padding: 12px 20px;
        width: 100%;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }
}

/* Enhanced Mobile Styles (768px and below) */
@media (max-width: 768px) {
    /* Hero Section Mobile */
    .hero {
        height: 90vh;
        padding: 0 4%;
    }

    .hero-title {
        font-size: 2.5rem;
        line-height: 1.1;
        margin-bottom: 15px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 25px;
        line-height: 1.4;
    }

    .enhanced-btn {
        padding: 12px 30px;
        font-size: 1rem;
        border-radius: 25px;
    }

    .floating-element {
        font-size: 1.5rem;
        opacity: 0.5;
    }

    /* Navigation Mobile */
    nav {
        height: 60px;
        padding: 0 4%;
    }

    .logo {
        font-size: 1.8rem;
    }

    /* Content Sections Mobile */
    .content-wrapper {
        padding: 80px 4% 40px;
    }

    .reminders-section,
    .nutrition-tracker-section,
    .saved-workouts-section,
    .workout-heatmap-section {
        padding: 25px 15px;
        margin: 0 5px 25px;
        border-radius: 15px;
    }

    .user-form-section {
        padding: 25px 20px;
        margin: 0 5px 30px;
    }

    /* Form Elements Mobile */
    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-group {
        min-width: 100%;
    }

    .form-control {
        padding: 12px 15px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Workout Heatmap Mobile */
    .heatmap-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 20px;
    }

    .heatmap-stats {
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .heatmap-stat {
        min-width: 70px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .heatmap-grid {
        grid-template-columns: repeat(26, 1fr);
        gap: 1px;
        margin: 15px 0;
    }

    .heatmap-day {
        width: 10px;
        height: 10px;
    }

    .heatmap-controls {
        justify-content: center;
        flex-wrap: wrap;
        gap: 8px;
    }

    .heatmap-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    /* Nutrition Section Mobile */
    .nutrition-summary {
        flex-direction: column;
        gap: 20px;
    }

    .calorie-card {
        padding: 20px;
    }

    .calorie-circle {
        width: 120px;
        height: 120px;
    }

    .macro-bar {
        height: 6px;
    }

    /* Hydration Tracker Mobile */
    .hydration-main {
        flex-direction: column;
        gap: 20px;
    }

    .hydration-stats {
        gap: 20px;
    }

    .hydration-controls {
        gap: 10px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .water-btn {
        padding: 10px 15px;
        font-size: 0.9rem;
        min-width: 80px;
    }

    /* Exercise List Mobile */
    .exercise-list {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .exercise-item {
        flex-direction: column;
        text-align: center;
    }

    .exercise-item img {
        width: 100%;
        max-width: 200px;
        margin-bottom: 15px;
    }

    /* Saved Workouts Mobile */
    .saved-workout-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .saved-workout-title {
        font-size: 1.1rem;
    }

    .saved-workout-info {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .saved-workout-stat {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    /* Notifications Mobile */
    .notification-panel {
        width: calc(100% - 20px);
        right: 10px;
        max-height: 70vh;
    }

    /* Particles Mobile Optimization */
    #particles-js {
        opacity: 0.3;
    }

    /* Chatbot Tablet */
    .chatbot-container {
        width: 400px;
        height: 500px;
        bottom: 20px;
        right: 20px;
    }

    .message-content {
        max-width: 80%;
    }
}

/* Small Mobile Devices (576px and below) */
@media (max-width: 576px) {
    /* Hero Section Small Mobile */
    .hero {
        height: 85vh;
        padding: 0 5%;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.1;
        margin-bottom: 12px;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 20px;
        padding: 0 10px;
    }

    .enhanced-btn {
        padding: 10px 25px;
        font-size: 0.95rem;
        width: 100%;
        max-width: 250px;
    }

    .floating-element {
        font-size: 1.2rem;
        opacity: 0.4;
    }

    /* Navigation Small Mobile */
    nav {
        height: 55px;
        padding: 0 5%;
    }

    .logo {
        font-size: 1.6rem;
    }

    /* Content Sections Small Mobile */
    .content-wrapper {
        padding: 70px 5% 30px;
    }

    .reminders-section,
    .nutrition-tracker-section,
    .saved-workouts-section,
    .workout-heatmap-section {
        padding: 20px 12px;
        margin: 0 2px 20px;
        border-radius: 12px;
    }

    .user-form-section {
        padding: 20px 15px;
        margin: 0 2px 25px;
    }

    /* Form Elements Small Mobile */
    .form-actions {
        flex-direction: column;
        gap: 10px;
    }

    .form-control {
        padding: 14px 16px;
        font-size: 16px;
        border-radius: 8px;
    }

    .submit-btn,
    .secondary-btn {
        width: 100%;
        padding: 12px;
        font-size: 1rem;
    }

    /* Search Box Small Mobile */
    .search-box {
        flex-direction: column;
        gap: 10px;
    }

    .searchbox {
        width: 100%;
        margin-bottom: 10px;
    }

    /* Day Selector Small Mobile */
    .day-selector {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    /* Workout Heatmap Small Mobile */
    .heatmap-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        text-align: center;
    }

    .heatmap-stat {
        min-width: auto;
        padding: 10px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .heatmap-grid {
        grid-template-columns: repeat(20, 1fr);
        gap: 1px;
        margin: 10px 0;
    }

    .heatmap-day {
        width: 8px;
        height: 8px;
    }

    .heatmap-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 80px;
    }

    /* Hydration Tracker Small Mobile */
    .water-bottle-container {
        width: 100px;
        height: 160px;
    }

    .hydration-controls {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .water-btn {
        width: 120px;
        padding: 8px 12px;
        font-size: 0.85rem;
    }

    .hydration-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        text-align: center;
    }

    .hydration-stat {
        background: rgba(255, 255, 255, 0.1);
        padding: 10px;
        border-radius: 8px;
    }

    /* Nutrition Small Mobile */
    .calorie-card {
        padding: 15px;
        text-align: center;
    }

    .calorie-circle {
        width: 100px;
        height: 100px;
        margin: 0 auto 15px;
    }

    .macro-item {
        margin-bottom: 12px;
    }

    .macro-bar {
        height: 5px;
        margin: 5px 0;
    }

    /* Exercise List Small Mobile */
    .exercise-item {
        padding: 15px;
        border-radius: 10px;
    }

    .exercise-item img {
        max-width: 150px;
        margin-bottom: 10px;
    }

    .exercise-item h3 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .exercise-item p {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    /* Saved Workouts Small Mobile */
    .saved-workout-item {
        padding: 15px;
        margin-bottom: 15px;
    }

    .saved-workout-title {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .saved-workout-info {
        flex-direction: column;
        gap: 6px;
        align-items: flex-start;
    }

    .saved-workout-stat {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    .saved-workout-actions {
        margin-top: 10px;
        gap: 8px;
    }

    .saved-workout-actions button {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    /* Notifications Small Mobile */
    .notification-panel {
        width: calc(100% - 10px);
        right: 5px;
        max-height: 60vh;
    }

    .notification-item {
        padding: 10px;
        font-size: 0.85rem;
    }

    /* Particles Extra Light on Small Mobile */
    #particles-js {
        opacity: 0.2;
    }

    /* Typography Small Mobile */
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.2rem; }
    h4 { font-size: 1rem; }

    /* Spacing Adjustments */
    .main-section {
        padding: 15px 0;
    }

    .section-title {
        font-size: 1.4rem;
        margin-bottom: 15px;
    }

    /* Chatbot Mobile */
    .chatbot-container {
        width: calc(100vw - 20px);
        height: calc(100vh - 100px);
        bottom: 10px;
        right: 10px;
        left: 10px;
        border-radius: 15px;
    }

    .chat-header {
        padding: 15px 20px;
        border-radius: 15px 15px 0 0;
    }

    .chat-header h3 {
        font-size: 1.1rem;
    }

    .chat-messages {
        padding: 15px;
    }

    .message-content {
        max-width: 85%;
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    .bot-avatar {
        width: 35px;
        height: 35px;
    }

    .chat-input {
        padding: 12px 15px;
        gap: 8px;
    }

    .chat-input input {
        padding: 10px 15px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .chat-input button {
        min-width: 40px;
        height: 40px;
    }

    #voice-btn {
        padding: 6px 10px;
        font-size: 0.75rem;
    }

    .quick-action-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
        margin: 2px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 6px;
    }

    .action-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
        text-align: center;
    }

    /* Settings Mobile */
    .settings-section {
        padding: 20px;
        margin: 10px;
    }

    .settings-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .export-buttons {
        flex-direction: column;
        width: 100%;
    }

    .settings-btn {
        width: 100%;
        text-align: center;
    }

    .storage-info {
        padding: 15px;
    }

    .storage-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    /* Reminders Mobile */
    .reminders-section {
        padding: 20px;
    }

    .reminder-form-container,
    .reminders-list-container {
        padding: 20px;
        margin: 10px 0;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .days-selector {
        justify-content: center;
    }

    .day-checkbox {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .reminder-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        padding: 15px;
    }

    .reminder-actions {
        width: 100%;
        justify-content: space-between;
    }

    .reminder-btn {
        flex: 1;
        text-align: center;
    }

    .permission-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
}

/* Touch Device Optimizations */
.touch-device button,
.touch-device .nav-link,
.touch-device .heatmap-day {
    min-height: 44px; /* Apple's recommended minimum touch target */
    min-width: 44px;
}

.touch-device .water-btn {
    min-height: 44px;
    padding: 10px 15px;
}

.touch-device .heatmap-btn {
    min-height: 44px;
    padding: 10px 20px;
}

/* Mobile Device Performance Optimizations */
.mobile-device * {
    -webkit-transform: translateZ(0); /* Force hardware acceleration */
    transform: translateZ(0);
}

.mobile-device .floating-element {
    animation-duration: 8s; /* Slower animations on mobile */
}

.mobile-device #particles-js {
    opacity: 0.2 !important;
}

/* Prevent zoom on input focus (iOS) */
@media screen and (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="number"],
    input[type="date"],
    input[type="password"],
    select,
    textarea {
        font-size: 16px !important;
    }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .heatmap-day {
        border-radius: 1px;
    }

    .water-bottle {
        border-width: 2px;
    }
}

/* Landscape Mobile Optimizations */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .hero {
        height: 100vh;
        padding: 0 5%;
    }

    .hero-title {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .enhanced-btn {
        padding: 8px 20px;
        font-size: 0.9rem;
    }

    .floating-element {
        display: none; /* Hide floating elements in landscape */
    }

    nav {
        height: 50px;
    }

    .logo {
        font-size: 1.4rem;
    }
}

/* Print Styles */
@media print {
    .hero,
    .floating-element,
    #particles-js,
    .mobile-menu-toggle,
    .notification-panel {
        display: none !important;
    }

    .workout-heatmap-section,
    .nutrition-tracker-section {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}
/* Utility Classes */
.hidden {
    display: none;
}