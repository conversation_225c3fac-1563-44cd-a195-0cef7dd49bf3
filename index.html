<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitAI - Your AI Fitness Partner</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/@hugeicons/icons"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="main-container">
        <!-- Navigation Bar -->
        <nav>
            <h1 class="logo">FitAI</h1>
            <div class="right-nav">
                <div class="nav-links" id="navLinks">
                    <a href="#" class="nav-link active" id="homeLink">Home</a>
                    <a href="#" class="nav-link" id="workoutsLink">My Workouts</a>
                    <a href="#" class="nav-link" id="nutritionLink">Nutrition Tracker</a>
                    <a href="#" class="nav-link" id="remindersLink">Reminders</a>
                    <a href="#" class="nav-link" id="settingsLink">⚙️ Settings</a>
                </div>
                <input class="searchbox" type="text" placeholder="Search exercises...">
                <button class="chat-btn" id="chatToggle">
                    <img src="chatbot.svg" alt="Chat">
                </button>
                <div class="notification-bell" id="notificationBell">
                    <span class="notification-count">0</span>
                    <img src="notification-bell.svg" alt="Notifications">
                </div>
                <div class="mobile-menu-toggle" id="mobileMenuToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <h1><span id="typewriter" data-text="Your Personal AI Fitness Coach">Your Personal AI Fitness Coach</span><span class="cursor">|</span></h1>
                <p>Get a personalized workout plan tailored to your goals and preferences </p>
                <button class="cta-btn" id="getStartedBtn">Get Started</button>
            </div>
        </section>

        <!-- Main Content Area -->
        <div class="content-wrapper">
            <!-- Home Section -->
            <div id="homeSection" class="main-section">
                <!-- User Form (initially hidden) -->
                <section id="userForm" class="user-form-section hidden">
                    <h2>Tell Us About Yourself</h2>
                    <form id="fitnessForm">
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" id="name" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="age">Age</label>
                            <input type="number" id="age" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="gender">Gender</label>
                            <select id="gender" class="form-control" required>
                                <option value="">Select</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="height">Height (cm)</label>
                            <input type="number" id="height" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="weight">Weight (kg)</label>
                            <input type="number" id="weight" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="goal">Your Fitness Goal</label>
                            <select id="goal" class="form-control" required>
                                <option value="">Select</option>
                                <option value="weightloss">Weight Loss</option>
                                <option value="muscle">Muscle Building</option>
                                <option value="toning">Body Toning</option>
                                <option value="endurance">Endurance</option>
                                <option value="strength">Strength</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="experience">Fitness Experience</label>
                            <select id="experience" class="form-control" required>
                                <option value="">Select</option>
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="time">Available Time per Day (minutes)</label>
                            <input type="number" id="time" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label for="equipment">Available Equipment</label>
                            <div class="checkbox-group">
                                <label><input type="checkbox" name="equipment" value="none"> No Equipment</label>
                                <label><input type="checkbox" name="equipment" value="dumbbells"> Dumbbells</label>
                                <label><input type="checkbox" name="equipment" value="bench"> Bench</label>
                                <label><input type="checkbox" name="equipment" value="barbell"> Barbell</label>
                                <label><input type="checkbox" name="equipment" value="machines"> Gym Machines</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="limitations">Any Physical Limitations/Injuries</label>
                            <textarea id="limitations" class="form-control"></textarea>
                        </div>
                        <button type="submit" class="submit-btn">Generate My Workout Plan</button>
                    </form>
                </section>

                <!-- Workout Plan Section (initially hidden) -->
                <section id="workoutPlan" class="workout-plan-section hidden">
                    <h2>Your Personalized Workout Plan</h2>
                    <div id="planInfo" class="plan-info"></div>
                    <div id="workoutSchedule" class="workout-schedule"></div>
                    <div class="exercise-gallery">
                        <h3>Recommended Exercises</h3>
                        <div id="exerciseList" class="exercise-list"></div>
                    </div>
                    <div class="action-buttons">
                        <button id="savePlanBtn" class="action-btn">Save Plan</button>
                        <button id="adjustPlanBtn" class="action-btn">Adjust Plan</button>
                        <button id="setRemindersBtn" class="action-btn">Set Workout Reminders</button>
                    </div>
                </section>

                <!-- Featured Exercises Gallery -->
                <section class="exercise-showcase">
                    <h2>Exercise Library</h2>
                    <div class="category-filters">
                        <button class="filter-btn active" data-category="all">All</button>
                        <button class="filter-btn" data-category="chest">Chest</button>
                        <button class="filter-btn" data-category="back">Back</button>
                        <button class="filter-btn" data-category="shoulder">Shoulders</button>
                        <button class="filter-btn" data-category="biceps">Biceps</button>
                        <button class="filter-btn" data-category="triceps">Triceps</button>
                        <button class="filter-btn" data-category="leg">Legs</button>
                    </div>
                    <div class="exercises-grid">
                        <!-- Chest exercises -->
                        <div class="exercise-card" data-category="chest">
                            <img src="chest1.gif" alt="Chest Exercise 1">
                            <h4>Incline Dumbbell Press</h4>
                        </div>
                        <div class="exercise-card" data-category="chest">
                            <img src="chest2.gif" alt="Chest Exercise 2">
                            <h4>Bench Press</h4>
                        </div>
                        <div class="exercise-card" data-category="chest">
                            <img src="chest3.gif" alt="Chest Exercise 3">
                            <h4>Push-Ups</h4>
                        </div>
                        <div class="exercise-card" data-category="chest">
                            <img src="chest4.gif" alt="Chest Exercise 4">
                            <h4>Machine Chest Fly</h4>
                        </div>
                        <div class="exercise-card" data-category="chest">
                            <img src="chest5.gif" alt="Chest Exercise 5">
                            <h4>Seated Chest Press</h4>
                        </div>
                        
                        <!-- Back exercises -->
                        <div class="exercise-card" data-category="back">
                            <img src="back1.gif" alt="Back Exercise 1">
                            <h4>Reverse Cable Fly</h4>
                        </div>
                        <div class="exercise-card" data-category="back">
                            <img src="back2.gif" alt="Back Exercise 2">
                            <h4>Bent Over Row</h4>
                        </div>
                        <div class="exercise-card" data-category="back">
                            <img src="back3.gif" alt="Back Exercise 3">
                            <h4>Lat Pulldown</h4>
                        </div>
                        <div class="exercise-card" data-category="back">
                            <img src="back4.gif" alt="Back Exercise 4">
                            <h4>Pull-Ups</h4>
                        </div>
                        <div class="exercise-card" data-category="back">
                            <img src="back5.gif" alt="Back Exercise 5">
                            <h4>Cable Row</h4>
                        </div>
                        
                        <!-- Shoulder exercises -->
                        <div class="exercise-card" data-category="shoulder">
                            <img src="shoulder1.gif" alt="Shoulder Exercise 1">
                            <h4>Overhead Press</h4>
                        </div>
                        <div class="exercise-card" data-category="shoulder">
                            <img src="shoulder2.gif" alt="Shoulder Exercise 2">
                            <h4>Lateral Raise</h4>
                        </div>
                        <div class="exercise-card" data-category="shoulder">
                            <img src="shoulder3.gif" alt="Shoulder Exercise 3">
                            <h4>Front Raise</h4>
                        </div>
                        <div class="exercise-card" data-category="shoulder">
                            <img src="shoulder4.gif" alt="Shoulder Exercise 4">
                            <h4>Face Pull</h4>
                        </div>
                        <div class="exercise-card" data-category="shoulder">
                            <img src="shoulder5.gif" alt="Shoulder Exercise 5">
                            <h4>Reverse Fly</h4>
                        </div>
                        
                        <!-- Biceps exercises -->
                        <div class="exercise-card" data-category="biceps">
                            <img src="biceps1.gif" alt="Biceps Exercise 1">
                            <h4>Barbell Curl</h4>
                        </div>
                        <div class="exercise-card" data-category="biceps">
                            <img src="biceps2.gif" alt="Biceps Exercise 2">
                            <h4>Hammer Curl</h4>
                        </div>
                        <div class="exercise-card" data-category="biceps">
                            <img src="biceps3.gif" alt="Biceps Exercise 3">
                            <h4>Preacher Curl</h4>
                        </div>
                        
                        <!-- Triceps exercises -->
                        <div class="exercise-card" data-category="triceps">
                            <img src="triceps1.gif" alt="Triceps Exercise 1">
                            <h4>Triceps Pushdown</h4>
                        </div>
                        <div class="exercise-card" data-category="triceps">
                            <img src="triceps2.gif" alt="Triceps Exercise 2">
                            <h4>Overhead Extension</h4>
                        </div>
                        
                        <!-- Leg exercises -->
                        <div class="exercise-card" data-category="leg">
                            <img src="leg.gif" alt="Leg Exercise 1">
                            <h4>Squats</h4>
                        </div>
                        <div class="exercise-card" data-category="leg">
                            <img src="leg2.gif" alt="Leg Exercise 2">
                            <h4>Dead Lift</h4>
                        </div>
                        <div class="exercise-card" data-category="leg">
                            <img src="leg3.gif" alt="Leg Exercise 3">
                            <h4>Lunges</h4>
                        </div>
                        <div class="exercise-card" data-category="leg">
                            <img src="leg4.gif" alt="Leg Exercise 4">
                            <h4>Leg Extensions</h4>
                        </div>
                        <div class="exercise-card" data-category="leg">
                            <img src="leg5.gif" alt="Leg Exercise 5">
                            <h4>Foot ups</h4>
                        </div>
                    </div>
                </section>
            </div>

            <!-- My Workouts Section (initially hidden) -->
            <div id="workoutsSection" class="main-section hidden">
                <!-- Workout Heatmap -->
                <section class="workout-heatmap-section">
                    <h2>🔥 Workout Activity</h2>
                    <div class="heatmap-container">
                        <div class="heatmap-header">
                            <div class="heatmap-stats">
                                <div class="heatmap-stat">
                                    <span class="stat-number" id="totalWorkouts">0</span>
                                    <span class="stat-label">Total Workouts</span>
                                </div>
                                <div class="heatmap-stat">
                                    <span class="stat-number" id="currentStreak">0</span>
                                    <span class="stat-label">Current Streak</span>
                                </div>
                                <div class="heatmap-stat">
                                    <span class="stat-number" id="longestStreak">0</span>
                                    <span class="stat-label">Longest Streak</span>
                                </div>
                                <div class="heatmap-stat">
                                    <span class="stat-number" id="thisWeekWorkouts">0</span>
                                    <span class="stat-label">This Week</span>
                                </div>
                            </div>
                            <div class="heatmap-controls">
                                <button class="heatmap-btn secondary" id="viewStatsBtn">📊 View Stats</button>
                            </div>
                        </div>

                        <div class="heatmap-calendar">
                            <div class="heatmap-months" id="heatmapMonths"></div>
                            <div class="heatmap-grid" id="heatmapGrid"></div>
                            <div class="heatmap-legend">
                                <span class="legend-label">Less</span>
                                <div class="legend-colors">
                                    <div class="legend-color" data-level="0"></div>
                                    <div class="legend-color" data-level="1"></div>
                                    <div class="legend-color" data-level="2"></div>
                                    <div class="legend-color" data-level="3"></div>
                                    <div class="legend-color" data-level="4"></div>
                                </div>
                                <span class="legend-label">More</span>
                            </div>
                        </div>

                        <div class="heatmap-tooltip" id="heatmapTooltip"></div>
                    </div>
                </section>

                <section class="saved-workouts-section">
                    <h2>My Saved Workout Plans</h2>
                    <div id="savedWorkoutsList" class="saved-workouts-list">
                        <p class="empty-state">You haven't saved any workout plans yet.</p>
                    </div>
                </section>
            </div>

            <!-- Nutrition Tracker Section (initially hidden) -->
            <div id="nutritionSection" class="main-section hidden">
                <section class="nutrition-tracker-section">
                    <h2>Nutrition & Calorie Tracker</h2>
                    
                    <div class="nutrition-summary">
                        <div class="calorie-card">
                            <h3>Today's Calories</h3>
                            <div class="calorie-circle">
                                <span id="currentCalories">0</span>
                                <span class="calorie-label">of <span id="targetCalories">2000</span> kcal</span>
                            </div>
                            <div class="macros">
                                <div class="macro">
                                    <span class="macro-label">Protein</span>
                                    <div class="macro-bar">
                                        <div class="macro-progress protein" id="proteinProgress"></div>
                                    </div>
                                    <span class="macro-value" id="proteinValue">0g</span>
                                </div>
                                <div class="macro">
                                    <span class="macro-label">Carbs</span>
                                    <div class="macro-bar">
                                        <div class="macro-progress carbs" id="carbsProgress"></div>
                                    </div>
                                    <span class="macro-value" id="carbsValue">0g</span>
                                </div>
                                <div class="macro">
                                    <span class="macro-label">Fat</span>
                                    <div class="macro-bar">
                                        <div class="macro-progress fat" id="fatProgress"></div>
                                    </div>
                                    <span class="macro-value" id="fatValue">0g</span>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="foodPickerInput">Choose from database</label>
                                    <input id="foodPickerInput" class="form-control" list="foodPickerList" placeholder="Start typing a food...">
                                    <datalist id="foodPickerList"></datalist>
                                </div>
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" id="fillFromPicker" class="secondary-btn">Fill from selection</button>
                                </div>
                                <div class="form-group">
                                    <label for="qrUpload">Upload QR image (optional)</label>
                                    <input id="qrUpload" type="file" class="form-control" accept="image/*">
                                    <span class="help-text">We will try to decode product name or calories from the QR.</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="meal-history">
                            <h3>Today's Food Log</h3>
                            <div id="mealList" class="meal-list">
                                <!-- Meals will be added here dynamically -->
                                <p class="empty-state">No meals logged today.</p>
                            </div>
                        </div>
                        <div class="vitals-card">
                            <h3>Wearable Vitals</h3>
                            <div class="vitals-row">
                                <div class="vital">
                                    <div class="vital-label">Heart Rate</div>
                                    <div class="vital-value"><span id="heartRateValue">--</span> bpm</div>
                                </div>
                                <div class="vital">
                                    <div class="vital-label">Battery</div>
                                    <div class="vital-value"><span id="batteryLevelValue">--</span>%</div>
                                </div>
                            </div>
                            <div class="vitals-actions">
                                <button type="button" class="secondary-btn" id="connectWearableBtn">Connect Wearable</button>
                                <button type="button" class="secondary-btn" id="disconnectWearableBtn">Disconnect</button>
                            </div>
                            <div class="vitals-status" id="deviceStatus">Status: Not connected</div>
                            <span class="help-text">Bluetooth works on https or localhost only. Grant permission when prompted.</span>
                        </div>
                    </div>

                    <!-- Hydration Tracker -->
                    <div class="hydration-tracker-section">
                        <h3>💧 Hydration Tracker</h3>
                        <div class="hydration-content">
                            <div class="hydration-reminder" id="hydrationReminder">
                                <strong>💧 Time to drink water!</strong> Stay hydrated for better performance.
                            </div>

                            <div class="hydration-main">
                                <div class="water-bottle-container">
                                    <div class="water-bottle">
                                        <div class="water-level" id="waterLevel"></div>
                                    </div>
                                </div>

                                <div class="hydration-stats">
                                    <div class="hydration-stat">
                                        <h4 id="currentWater">0</h4>
                                        <p>Current (ml)</p>
                                    </div>
                                    <div class="hydration-stat">
                                        <h4 id="waterGoal">3000</h4>
                                        <p>Goal (ml)</p>
                                    </div>
                                    <div class="hydration-stat">
                                        <h4 id="waterPercentage">0%</h4>
                                        <p>Progress</p>
                                    </div>
                                </div>
                            </div>

                            <div class="hydration-controls">
                                <button class="water-btn" onclick="addWater(250)">+250ml</button>
                                <button class="water-btn" onclick="addWater(500)">+500ml</button>
                                <button class="water-btn" onclick="addWater(750)">+750ml</button>
                                <button class="water-btn reset" onclick="resetWater()">Reset</button>
                            </div>

                            <p class="hydration-tip">
                                💡 Tip: Drink water regularly throughout the day. You'll get reminders every 2 hours!
                            </p>
                        </div>
                    </div>

                    <div class="add-food-section">
                        <h3>Add Food</h3>
                        <form id="foodForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="foodName">Food Name</label>
                                    <input type="text" id="foodName" class="form-control" placeholder="e.g. Grilled Chicken Breast" required>
                                </div>
                                <div class="form-group">
                                    <label for="mealType">Meal Type</label>
                                    <select id="mealType" class="form-control" required>
                                        <option value="breakfast">Breakfast</option>
                                        <option value="lunch">Lunch</option>
                                        <option value="dinner">Dinner</option>
                                        <option value="snack">Snack</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="portionSize">Quantity (grams)</label>
                                    <input type="number" id="portionSize" class="form-control" value="100" min="1" required>
                                    <span class="help-text">Calories are shown per 100g. Adjust quantity as needed.</span>
                                </div>
                                <div class="form-group">
                                    <label for="calories">Calories</label>
                                    <input type="number" id="calories" class="form-control" placeholder="kcal" required>
                                    <span id="totalCalories" class="help-text"></span>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="protein">Protein (g)</label>
                                    <input type="number" id="protein" class="form-control" placeholder="g">
                                </div>
                                <div class="form-group">
                                    <label for="carbs">Carbs (g)</label>
                                    <input type="number" id="carbs" class="form-control" placeholder="g">
                                </div>
                                <div class="form-group">
                                    <label for="fat">Fat (g)</label>
                                    <input type="number" id="fat" class="form-control" placeholder="g">
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" id="searchFoodBtn" class="secondary-btn">Search Food Database</button>
                                <button type="submit" class="submit-btn">Add Food</button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="food-search-results hidden" id="foodSearchResults">
                        <h3>Search Results</h3>
                        <div class="search-box">
                            <input type="text" id="foodSearchInput" placeholder="Search for a food...">
                            <button id="performFoodSearch">Search</button>
                        </div>
                        <div id="foodResultsList" class="food-results-list">
                            <!-- Search results will appear here -->
                        </div>
                    </div>
                    
                    <div class="nutrition-history">
                        <h3>Calorie History</h3>
                        <div class="chart-container">
                            <canvas id="calorieChart"></canvas>
                        </div>
                    </div>
                </section>
            </div>
            
            <!-- Reminders Section (initially hidden) -->
            <div id="remindersSection" class="main-section hidden">
                <section class="reminders-section">
                    <h2>⏰ Fitness Reminders</h2>
                    <p class="section-description">Set up personalized reminders to stay on track with your fitness goals.</p>

                    <!-- Add Reminder Form -->
                    <div class="reminder-form-container">
                        <h3>📝 Create New Reminder</h3>
                        <form id="reminderForm" class="reminder-form">
                            <div class="form-group">
                                <label for="reminderTitle">Reminder Title</label>
                                <input type="text" id="reminderTitle" placeholder="e.g., Morning Workout" required>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="reminderType">Type</label>
                                    <select id="reminderType" required>
                                        <option value="workout">🏋️ Workout</option>
                                        <option value="nutrition">🍎 Nutrition</option>
                                        <option value="hydration">💧 Hydration</option>
                                        <option value="general">📋 General</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="reminderTime">Time</label>
                                    <input type="time" id="reminderTime" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="reminderNote">Note (Optional)</label>
                                <textarea id="reminderNote" placeholder="Additional details about this reminder..."></textarea>
                            </div>

                            <div class="form-group">
                                <label>Days of the Week</label>
                                <div class="days-selector">
                                    <label class="day-checkbox">
                                        <input type="checkbox" value="monday" checked>
                                        <span>Mon</span>
                                    </label>
                                    <label class="day-checkbox">
                                        <input type="checkbox" value="tuesday" checked>
                                        <span>Tue</span>
                                    </label>
                                    <label class="day-checkbox">
                                        <input type="checkbox" value="wednesday" checked>
                                        <span>Wed</span>
                                    </label>
                                    <label class="day-checkbox">
                                        <input type="checkbox" value="thursday" checked>
                                        <span>Thu</span>
                                    </label>
                                    <label class="day-checkbox">
                                        <input type="checkbox" value="friday" checked>
                                        <span>Fri</span>
                                    </label>
                                    <label class="day-checkbox">
                                        <input type="checkbox" value="saturday">
                                        <span>Sat</span>
                                    </label>
                                    <label class="day-checkbox">
                                        <input type="checkbox" value="sunday">
                                        <span>Sun</span>
                                    </label>
                                </div>
                            </div>

                            <button type="submit" class="btn-primary">
                                <span>⏰ Create Reminder</span>
                            </button>
                        </form>
                    </div>

                    <!-- Reminders List -->
                    <div class="reminders-list-container">
                        <h3>📋 Your Reminders</h3>
                        <div id="remindersList" class="reminders-list">
                            <div class="no-reminders">
                                <div class="no-reminders-icon">⏰</div>
                                <h4>No reminders set yet</h4>
                                <p>Create your first reminder above to get started!</p>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Permissions -->
                    <div id="reminderPermissions" class="notification-permissions">
                        <div class="permission-card">
                            <div class="permission-icon">🔔</div>
                            <div class="permission-content">
                                <h4>Enable Browser Notifications</h4>
                                <p>Allow notifications to receive your fitness reminders even when the app is closed.</p>
                                <button id="enableNotifications" class="btn-secondary">Enable Notifications</button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Settings Section (initially hidden) -->
            <div id="settingsSection" class="main-section hidden">
                <section class="settings-section">
                    <h2>⚙️ Settings & Data Management</h2>

                    <!-- Data Management -->
                    <div class="settings-group">
                        <h3>📊 Data Management</h3>
                        <div class="settings-item">
                            <div class="setting-info">
                                <h4>Backup Your Data</h4>
                                <p>Download a backup of all your fitness data</p>
                            </div>
                            <button class="settings-btn" id="downloadBackupBtn">📥 Download Backup</button>
                        </div>

                        <div class="settings-item">
                            <div class="setting-info">
                                <h4>Restore Data</h4>
                                <p>Restore your data from a backup file</p>
                            </div>
                            <div class="file-input-wrapper">
                                <input type="file" id="restoreFileInput" accept=".json" style="display: none;">
                                <button class="settings-btn secondary" id="restoreDataBtn">📤 Restore from File</button>
                            </div>
                        </div>

                        <div class="settings-item">
                            <div class="setting-info">
                                <h4>Export Data</h4>
                                <p>Export your data in different formats</p>
                            </div>
                            <div class="export-buttons">
                                <button class="settings-btn" id="exportJsonBtn">📄 Export JSON</button>
                                <button class="settings-btn" id="exportCsvBtn">📊 Export CSV</button>
                            </div>
                        </div>

                        <div class="settings-item">
                            <div class="setting-info">
                                <h4>Clear All Data</h4>
                                <p>⚠️ This will permanently delete all your data</p>
                            </div>
                            <button class="settings-btn danger" id="clearDataBtn">🗑️ Clear All Data</button>
                        </div>
                    </div>

                    <!-- Storage Info -->
                    <div class="settings-group">
                        <h3>💾 Storage Information</h3>
                        <div class="storage-info">
                            <div class="storage-item">
                                <span class="storage-label">Workout Plans:</span>
                                <span class="storage-value" id="workoutPlansCount">0</span>
                            </div>
                            <div class="storage-item">
                                <span class="storage-label">Workout Sessions:</span>
                                <span class="storage-value" id="workoutSessionsCount">0</span>
                            </div>
                            <div class="storage-item">
                                <span class="storage-label">Meals Logged:</span>
                                <span class="storage-value" id="mealsCount">0</span>
                            </div>
                            <div class="storage-item">
                                <span class="storage-label">Reminders:</span>
                                <span class="storage-value" id="remindersCount">0</span>
                            </div>
                            <div class="storage-item">
                                <span class="storage-label">Last Backup:</span>
                                <span class="storage-value" id="lastBackupDate">Never</span>
                            </div>
                            <div class="storage-item">
                                <span class="storage-label">Data Size:</span>
                                <span class="storage-value" id="dataSize">0 KB</span>
                            </div>
                        </div>
                    </div>

                    <!-- App Settings -->
                    <div class="settings-group">
                        <h3>🔧 App Settings</h3>
                        <div class="settings-item">
                            <div class="setting-info">
                                <h4>Auto Backup</h4>
                                <p>Automatically backup your data daily</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="autoBackupToggle" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>

                        <div class="settings-item">
                            <div class="setting-info">
                                <h4>Data Retention</h4>
                                <p>How long to keep old data (days)</p>
                            </div>
                            <select class="settings-select" id="dataRetentionSelect">
                                <option value="30">30 days</option>
                                <option value="60">60 days</option>
                                <option value="90" selected>90 days</option>
                                <option value="180">180 days</option>
                                <option value="365">1 year</option>
                                <option value="-1">Forever</option>
                            </select>
                        </div>
                    </div>
                </section>
            </div>
        </div>

        <!-- Notification Panel -->
        <div id="notificationPanel" class="notification-panel hidden">
            <div class="notification-header">
                <h3>Notifications</h3>
                <button id="clearAllNotifications">Clear All</button>
            </div>
            <div id="notificationList" class="notification-list">
                <!-- Notifications will be added here dynamically -->
                <p class="empty-state">No notifications at the moment.</p>
            </div>
        </div>

        <!-- Chatbot Component -->
        <div id="chatbot" class="chatbot-container hidden">
            <div class="chat-header">
                <h3>Fitness Assistant</h3>
                <button id="closeChat" class="close-btn">
                    <img src="cross.svg" alt="Close">
                </button>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                    <img src="ai.png" class="bot-avatar">
                    <div class="message-content">Hello! I'm your AI fitness assistant. How can I help you today?</div>
                </div>
            </div>
            <div class="chat-input">
                <input type="text" id="userMessage" placeholder="Ask me anything about fitness...">
                <button id="voice-btn">🎤 Speak</button>
                <button id="sendMessage">
                    <img src="arrow.svg" alt="Send">
                </button>
            </div>
        </div>
    </div>
    <script>
        const voiceBtn = document.getElementById("voice-btn");
        
        voiceBtn.addEventListener("click", () => {
            const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
            recognition.lang = 'en-US';
            recognition.start();
        
            recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                console.log("User said:", transcript);
        
                // Display result to the user
                alert(`You said: ${transcript}`);
            };
        });
    </script>
        
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
    <script src="script.js"></script>
</body>
</html>